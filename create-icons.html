<!DOCTYPE html>
<html>
<head>
    <title>Create Stoke Cloner Icons</title>
</head>
<body>
    <h2>Stoke Cloner Icon Generator</h2>
    <p>This will generate placeholder icons. Open this file in a browser and click the button.</p>
    <button onclick="generateIcons()">Generate Icons</button>
    <div id="output"></div>

    <script>
        function generateIcons() {
            const sizes = [16, 32, 48, 128];
            const output = document.getElementById('output');
            output.innerHTML = '<h3>Generated Icons:</h3>';
            
            sizes.forEach(size => {
                const canvas = document.createElement('canvas');
                canvas.width = size;
                canvas.height = size;
                const ctx = canvas.getContext('2d');
                
                // Orange background
                ctx.fillStyle = '#f97316';
                ctx.fillRect(0, 0, size, size);
                
                // White "S" letter
                ctx.fillStyle = '#ffffff';
                ctx.font = `bold ${size * 0.6}px Arial`;
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText('S', size / 2, size / 2);
                
                // Create download link
                canvas.toBlob(blob => {
                    const url = URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = `icon${size}.png`;
                    link.textContent = `Download icon${size}.png`;
                    link.style.display = 'block';
                    link.style.margin = '5px 0';
                    output.appendChild(link);
                });
            });
        }
    </script>
</body>
</html>
