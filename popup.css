/* Base styles matching the Stoke Cloner web app */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  width: 400px;
  min-height: 500px;
  background: #0f172a;
  color: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  line-height: 1.5;
}

.container {
  padding: 16px;
}

/* Header */
.header {
  text-align: center;
  margin-bottom: 24px;
}

.title {
  font-size: 20px;
  font-weight: 700;
  color: #ffffff;
}

/* Input Groups */
.input-group {
  margin-bottom: 16px;
}

.label {
  display: block;
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
}

/* URL Input */
.url-container {
  display: flex;
  gap: 8px;
  align-items: center;
}

.url-input {
  flex: 1;
  padding: 12px;
  background: #1e293b;
  border: 1px solid #334155;
  border-radius: 12px;
  color: #ffffff;
  font-size: 14px;
  outline: none;
  transition: all 0.2s;
}

.url-input:focus {
  border-color: #f97316;
  box-shadow: 0 0 0 2px rgba(249, 115, 22, 0.2);
}

.edit-btn {
  padding: 8px 12px;
  background: #334155;
  border: none;
  border-radius: 8px;
  color: #ffffff;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.edit-btn:hover {
  background: #475569;
}

/* Range Slider */
.slider-container {
  margin-bottom: 8px;
}

.slider {
  width: 100%;
  height: 8px;
  background: #334155;
  border-radius: 4px;
  appearance: none;
  cursor: pointer;
  outline: none;
}

.slider::-webkit-slider-thumb {
  appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #f97316;
  cursor: pointer;
  border: 2px solid #1e293b;
  transition: background-color 0.2s;
}

.slider::-webkit-slider-thumb:hover {
  background: #ea580c;
}

.slider::-moz-range-thumb {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #f97316;
  cursor: pointer;
  border: 2px solid #1e293b;
  transition: background-color 0.2s;
}

.slider-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 4px;
  font-size: 12px;
  color: #94a3b8;
}

.help-text {
  font-size: 12px;
  color: #94a3b8;
  margin-top: 4px;
}

/* Toggle Switch */
.toggle-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.toggle-info {
  flex: 1;
}

.toggle-title {
  font-size: 14px;
  font-weight: 500;
  color: #ffffff;
  margin-bottom: 2px;
}

.toggle-description {
  font-size: 12px;
  color: #94a3b8;
}

.toggle-switch {
  position: relative;
  width: 44px;
  height: 24px;
  background: #475569;
  border-radius: 12px;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
  outline: none;
}

.toggle-switch:focus {
  box-shadow: 0 0 0 2px rgba(249, 115, 22, 0.2);
}

.toggle-switch.active {
  background: #f97316;
}

.toggle-slider {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 20px;
  background: #ffffff;
  border-radius: 50%;
  transition: transform 0.2s;
}

.toggle-switch.active .toggle-slider {
  transform: translateX(20px);
}

/* Clone Button */
.clone-button {
  width: 100%;
  padding: 16px;
  background: #f97316;
  color: #ffffff;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  outline: none;
  margin-bottom: 16px;
}

.clone-button:hover:not(:disabled) {
  background: #ea580c;
}

.clone-button:disabled {
  background: #475569;
  color: #94a3b8;
  cursor: not-allowed;
}

/* Spinner Animation */
.spinner {
  display: flex;
  align-items: center;
  justify-content: center;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Progress Section */
.progress-container {
  background: #1e293b;
  border: 1px solid #334155;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
}

.progress-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.status-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-icon.loading {
  background: #f97316;
}

.status-icon.complete {
  background: #10b981;
}

.status-icon.error {
  background: #ef4444;
}

.progress-text {
  font-size: 14px;
  font-weight: 500;
  color: #ffffff;
}

.progress-bar-bg {
  width: 100%;
  height: 8px;
  background: #334155;
  border-radius: 4px;
  margin: 8px 0;
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  background: #f97316;
  border-radius: 4px;
  transition: width 0.3s ease;
  width: 0%;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 12px;
}

.action-btn {
  width: 100%;
  padding: 12px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  transition: background-color 0.2s;
  outline: none;
}

.action-btn.primary {
  background: #f97316;
  color: #ffffff;
}

.action-btn.primary:hover {
  background: #ea580c;
}

.action-btn.secondary {
  background: #334155;
  color: #ffffff;
}

.action-btn.secondary:hover {
  background: #475569;
}

.btn-icon {
  width: 16px;
  height: 16px;
}

.reset-button {
  width: 100%;
  padding: 12px;
  background: #334155;
  color: #ffffff;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-top: 12px;
  outline: none;
}

.reset-button:hover {
  background: #475569;
}

/* Error Container */
.error-container {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 8px;
  padding: 12px;
  color: #fca5a5;
  font-size: 12px;
  margin: 8px 0;
}

/* Footer */
.footer {
  text-align: center;
  margin-top: 16px;
}

.settings-link {
  background: none;
  border: none;
  color: #94a3b8;
  font-size: 12px;
  cursor: pointer;
  transition: color 0.2s;
  outline: none;
}

.settings-link:hover {
  color: #f97316;
}

/* Utility Classes */
.hidden {
  display: none !important;
}
