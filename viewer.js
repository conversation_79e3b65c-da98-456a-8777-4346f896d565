// Viewer functionality for Stoke Cloner Chrome Extension
class StokeViewer {
  constructor() {
    this.cloneId = null;
    this.cloneData = null;
    this.blobUrls = new Map();
    
    this.initializeElements();
    this.setupEventListeners();
    this.loadCloneData();
  }
  
  initializeElements() {
    this.urlInfo = document.getElementById('url-info');
    this.downloadBtn = document.getElementById('download-btn');
    this.refreshBtn = document.getElementById('refresh-btn');
    this.loading = document.getElementById('loading');
    this.error = document.getElementById('error');
    this.errorMessage = document.getElementById('error-message');
    this.viewerFrame = document.getElementById('viewer-frame');
  }
  
  setupEventListeners() {
    this.downloadBtn.addEventListener('click', () => this.downloadZip());
    this.refreshBtn.addEventListener('click', () => this.refreshViewer());
  }
  
  async loadCloneData() {
    try {
      // Get clone ID from URL parameters
      const urlParams = new URLSearchParams(window.location.search);
      this.cloneId = urlParams.get('cloneId');
      
      if (!this.cloneId) {
        throw new Error('No clone ID provided');
      }
      
      // Get clone data from storage
      const result = await chrome.storage.local.get([this.cloneId]);
      const storedData = result[this.cloneId];
      
      if (!storedData || !storedData.cloneData) {
        throw new Error('Clone data not found');
      }
      
      this.cloneData = storedData.cloneData;
      this.updateUrlInfo();
      
      // Process and display the cloned website
      await this.processAndDisplayWebsite();
      
    } catch (error) {
      console.error('Error loading clone data:', error);
      this.showError(error.message);
    }
  }
  
  updateUrlInfo() {
    if (this.cloneData) {
      this.urlInfo.textContent = `Viewing: ${this.cloneData.title || this.cloneData.url}`;
    }
  }
  
  async processAndDisplayWebsite() {
    try {
      console.log('Processing website for viewing...');
      
      // Create blob URLs for all assets
      await this.createBlobUrls();
      
      // Process HTML to use blob URLs
      const processedHtml = this.processHtmlForViewing();
      
      // Create blob URL for the processed HTML
      const htmlBlob = new Blob([processedHtml], { type: 'text/html' });
      const htmlBlobUrl = URL.createObjectURL(htmlBlob);
      
      // Store the HTML blob URL for cleanup
      this.blobUrls.set('main-html', htmlBlobUrl);
      
      // Load the processed HTML in the iframe
      this.viewerFrame.src = htmlBlobUrl;
      
      // Show the iframe and hide loading
      this.loading.classList.add('hidden');
      this.viewerFrame.classList.remove('hidden');
      
      console.log('Website loaded successfully');
      
    } catch (error) {
      console.error('Error processing website:', error);
      this.showError('Failed to process website: ' + error.message);
    }
  }
  
  async createBlobUrls() {
    // Process images
    for (const image of this.cloneData.assets.images) {
      if (image.dataUrl) {
        try {
          const response = await fetch(image.dataUrl);
          const blob = await response.blob();
          const blobUrl = URL.createObjectURL(blob);
          this.blobUrls.set(image.url, blobUrl);
        } catch (e) {
          console.warn('Failed to create blob URL for image:', image.url, e);
        }
      }
    }
    
    // Process stylesheets
    for (const stylesheet of this.cloneData.assets.stylesheets) {
      if (stylesheet.content) {
        const blob = new Blob([stylesheet.content], { type: 'text/css' });
        const blobUrl = URL.createObjectURL(blob);
        this.blobUrls.set(stylesheet.url, blobUrl);
      }
    }
    
    // Process scripts
    for (const script of this.cloneData.assets.scripts) {
      if (script.content) {
        const blob = new Blob([script.content], { type: 'application/javascript' });
        const blobUrl = URL.createObjectURL(blob);
        this.blobUrls.set(script.url, blobUrl);
      }
    }
  }
  
  processHtmlForViewing() {
    let html = this.cloneData.html;
    
    // Replace asset URLs with blob URLs
    for (const [originalUrl, blobUrl] of this.blobUrls) {
      if (originalUrl !== 'main-html') {
        // Use a more precise replacement to avoid partial matches
        const escapedUrl = originalUrl.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        const regex = new RegExp(`(['"])${escapedUrl}\\1`, 'g');
        html = html.replace(regex, `$1${blobUrl}$1`);
        
        // Also handle URLs without quotes
        const regexNoQuotes = new RegExp(`\\b${escapedUrl}\\b`, 'g');
        html = html.replace(regexNoQuotes, blobUrl);
      }
    }
    
    // Add base tag to handle relative URLs
    const baseTag = `<base href="${this.cloneData.url}">`;
    html = html.replace(/<head>/i, `<head>${baseTag}`);
    
    return html;
  }
  
  async downloadZip() {
    try {
      // Get the stored download URL
      const result = await chrome.storage.local.get([this.cloneId]);
      const storedData = result[this.cloneId];
      
      if (storedData && storedData.downloadUrl) {
        // Create a temporary link to trigger download
        const link = document.createElement('a');
        link.href = storedData.downloadUrl;
        link.download = storedData.filename || 'website_clone.zip';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else {
        throw new Error('Download URL not found');
      }
    } catch (error) {
      console.error('Download error:', error);
      alert('Failed to download ZIP file: ' + error.message);
    }
  }
  
  refreshViewer() {
    // Clear existing blob URLs
    for (const blobUrl of this.blobUrls.values()) {
      URL.revokeObjectURL(blobUrl);
    }
    this.blobUrls.clear();
    
    // Hide iframe and show loading
    this.viewerFrame.classList.add('hidden');
    this.error.classList.add('hidden');
    this.loading.classList.remove('hidden');
    
    // Reload the clone data
    this.loadCloneData();
  }
  
  showError(message) {
    this.loading.classList.add('hidden');
    this.viewerFrame.classList.add('hidden');
    this.errorMessage.textContent = message;
    this.error.classList.remove('hidden');
  }
  
  // Cleanup blob URLs when page unloads
  cleanup() {
    for (const blobUrl of this.blobUrls.values()) {
      URL.revokeObjectURL(blobUrl);
    }
  }
}

// Initialize viewer when page loads
document.addEventListener('DOMContentLoaded', () => {
  const viewer = new StokeViewer();
  
  // Cleanup on page unload
  window.addEventListener('beforeunload', () => {
    viewer.cleanup();
  });
});
