// Background service worker for Stoke Cloner Chrome Extension

// Extension installation and setup
chrome.runtime.onInstalled.addListener(() => {
  console.log('Stoke Cloner extension installed');
  
  // Create context menu
  chrome.contextMenus.create({
    id: "cloneWebsite",
    title: "Clone this website with Stoke Cloner",
    contexts: ["page"]
  });
  
  // Set default settings
  chrome.storage.sync.set({
    depth: 1,
    assetsOnly: false,
    serverUrl: 'http://localhost:3000'
  });
});

// Handle context menu clicks
chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === "cloneWebsite") {
    // Open the extension popup
    chrome.action.openPopup();
  }
});

// Handle messages from popup and content scripts
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('Background received message:', request);
  
  switch (request.action) {
    case "getCurrentTab":
      handleGetCurrentTab(sendResponse);
      return true; // Keep message channel open for async response
      
    case "startCloning":
      handleCloning(request.data, sendResponse);
      return true;
      
    case "viewLocally":
      handleViewLocally(request.downloadUrl, sendResponse);
      return true;
      
    default:
      console.warn('Unknown action:', request.action);
      sendResponse({success: false, error: 'Unknown action'});
  }
});

// Get current active tab information
async function handleGetCurrentTab(sendResponse) {
  try {
    const tabs = await chrome.tabs.query({active: true, currentWindow: true});
    if (tabs.length > 0) {
      const tab = tabs[0];
      sendResponse({
        success: true,
        url: tab.url,
        title: tab.title,
        id: tab.id
      });
    } else {
      sendResponse({success: false, error: 'No active tab found'});
    }
  } catch (error) {
    console.error('Error getting current tab:', error);
    sendResponse({success: false, error: error.message});
  }
}

// Handle website cloning by communicating with local Stoke Cloner server
async function handleCloning(data, sendResponse) {
  try {
    console.log('Starting cloning process for:', data.url);
    
    // Get server URL from settings
    const settings = await chrome.storage.sync.get(['serverUrl']);
    const serverUrl = settings.serverUrl || 'http://localhost:3000';
    
    // Make request to local Stoke Cloner server
    const response = await fetch(`${serverUrl}/api/clone`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        url: data.url,
        depth: data.depth,
        assetsOnly: data.assetsOnly
      })
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(errorText || `Server responded with ${response.status}`);
    }
    
    // Get the blob data
    const blob = await response.blob();
    
    // Create download URL
    const downloadUrl = URL.createObjectURL(blob);
    
    // Generate filename
    const filename = `${data.hostname.replace(/[^a-zA-Z0-9]/g, '_')}_clone_${Date.now()}.zip`;
    
    // Use Chrome Downloads API to download the file
    const downloadId = await chrome.downloads.download({
      url: downloadUrl,
      filename: filename,
      saveAs: false // Auto-download to default location
    });
    
    console.log('Download started with ID:', downloadId);
    
    // Store download info for potential local viewing
    await chrome.storage.local.set({
      [`download_${downloadId}`]: {
        url: downloadUrl,
        filename: filename,
        originalUrl: data.url,
        timestamp: Date.now()
      }
    });
    
    sendResponse({
      success: true,
      downloadId: downloadId,
      downloadUrl: downloadUrl,
      filename: filename
    });
    
  } catch (error) {
    console.error('Cloning error:', error);
    
    // Check if it's a connection error to the local server
    if (error.message.includes('fetch')) {
      sendResponse({
        success: false,
        error: 'Cannot connect to Stoke Cloner server. Please ensure the web app is running on localhost:3000'
      });
    } else {
      sendResponse({
        success: false,
        error: error.message
      });
    }
  }
}

// Handle local viewing by starting a local server
async function handleViewLocally(downloadUrl, sendResponse) {
  try {
    console.log('Starting local server for:', downloadUrl);
    
    // Get server URL from settings
    const settings = await chrome.storage.sync.get(['serverUrl']);
    const serverUrl = settings.serverUrl || 'http://localhost:3000';
    
    // First, we need to get the blob data from the download URL
    const response = await fetch(downloadUrl);
    const blob = await response.blob();
    
    // Create FormData to send to the local server
    const formData = new FormData();
    formData.append('file', blob, 'website_clone.zip');
    
    // Send to local server's local-server endpoint
    const serverResponse = await fetch(`${serverUrl}/api/local-server`, {
      method: 'POST',
      body: formData
    });
    
    if (!serverResponse.ok) {
      throw new Error('Failed to start local server');
    }
    
    const result = await serverResponse.json();
    
    sendResponse({
      success: true,
      localUrl: result.url,
      port: result.port
    });
    
  } catch (error) {
    console.error('Local server error:', error);
    sendResponse({
      success: false,
      error: 'Failed to start local server. Ensure Stoke Cloner web app is running on localhost:3000'
    });
  }
}

// Clean up old download URLs to prevent memory leaks
chrome.runtime.onStartup.addListener(async () => {
  try {
    const items = await chrome.storage.local.get();
    const now = Date.now();
    const oneDay = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
    
    for (const [key, value] of Object.entries(items)) {
      if (key.startsWith('download_') && value.timestamp) {
        if (now - value.timestamp > oneDay) {
          // Clean up old download data
          await chrome.storage.local.remove(key);
          if (value.url) {
            URL.revokeObjectURL(value.url);
          }
        }
      }
    }
  } catch (error) {
    console.error('Error cleaning up old downloads:', error);
  }
});

// Handle download completion
chrome.downloads.onChanged.addListener((downloadDelta) => {
  if (downloadDelta.state && downloadDelta.state.current === 'complete') {
    console.log('Download completed:', downloadDelta.id);
  }
});

// Error handling for unhandled promise rejections
self.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection in background script:', event.reason);
});
