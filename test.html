<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Page for Stoke Cloner</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .content {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        
        .image-container {
            text-align: center;
            margin: 20px 0;
        }
        
        .test-image {
            max-width: 200px;
            border-radius: 10px;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .feature {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔧 Stoke Cloner Test Page</h1>
        <p>This is a simple test page to verify the standalone extension works correctly.</p>
    </div>
    
    <div class="content">
        <h2>Test Content</h2>
        <p>This page contains various elements to test the cloning functionality:</p>
        
        <ul>
            <li>Inline CSS styles (should be captured)</li>
            <li>Text content and HTML structure</li>
            <li>Data URL image (should be included)</li>
            <li>Inline JavaScript (should be captured)</li>
        </ul>
        
        <div class="image-container">
            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjk3MzE2Ii8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxOCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj5UZXN0IEltYWdlPC90ZXh0Pgo8L3N2Zz4=" alt="Test Image" class="test-image">
        </div>
        
        <div class="features">
            <div class="feature">
                <h3>✅ HTML Structure</h3>
                <p>Complete page structure with semantic elements</p>
            </div>
            
            <div class="feature">
                <h3>🎨 CSS Styles</h3>
                <p>Inline styles with gradients and modern CSS</p>
            </div>
            
            <div class="feature">
                <h3>📷 Images</h3>
                <p>Data URL images that should be preserved</p>
            </div>
            
            <div class="feature">
                <h3>⚡ JavaScript</h3>
                <p>Interactive elements and scripts</p>
            </div>
        </div>
        
        <div style="margin-top: 30px; text-align: center;">
            <button onclick="testFunction()" style="background: #f97316; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                Test Button
            </button>
            <p id="test-output">Click the button to test JavaScript!</p>
        </div>
    </div>
    
    <script>
        function testFunction() {
            document.getElementById('test-output').textContent = 'JavaScript is working! ✅';
            console.log('Test function executed successfully');
        }
        
        // Add some dynamic content
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test page loaded successfully');
            
            // Add timestamp
            const timestamp = new Date().toLocaleString();
            const timestampElement = document.createElement('p');
            timestampElement.textContent = `Page loaded at: ${timestamp}`;
            timestampElement.style.textAlign = 'center';
            timestampElement.style.marginTop = '20px';
            timestampElement.style.fontSize = '14px';
            timestampElement.style.opacity = '0.8';
            document.body.appendChild(timestampElement);
        });
    </script>
</body>
</html>
