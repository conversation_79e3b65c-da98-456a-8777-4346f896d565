// Viewer functionality for Stoke Cloner Chrome Extension
class StokeViewer {
  constructor() {
    this.cloneId = null;
    this.cloneData = null;
    this.blobUrls = new Map();
    
    this.initializeElements();
    this.setupEventListeners();
    this.loadCloneData();
  }
  
  initializeElements() {
    this.urlInfo = document.getElementById('url-info');
    this.downloadBtn = document.getElementById('download-btn');
    this.refreshBtn = document.getElementById('refresh-btn');
    this.loading = document.getElementById('loading');
    this.error = document.getElementById('error');
    this.errorMessage = document.getElementById('error-message');
    this.viewerFrame = document.getElementById('viewer-frame');
  }
  
  setupEventListeners() {
    this.downloadBtn.addEventListener('click', () => this.downloadZip());
    this.refreshBtn.addEventListener('click', () => this.refreshViewer());
  }
  
  async loadCloneData() {
    try {
      // Get clone ID from URL parameters
      const urlParams = new URLSearchParams(window.location.search);
      this.cloneId = urlParams.get('cloneId');
      
      if (!this.cloneId) {
        throw new Error('No clone ID provided');
      }
      
      console.log('Loading clone data for ID:', this.cloneId);
      
      // Get clone data from storage
      const result = await chrome.storage.local.get([this.cloneId]);
      const storedData = result[this.cloneId];
      
      console.log('Stored data:', storedData);
      
      if (!storedData) {
        throw new Error('Clone data not found');
      }

      if (storedData.tooLarge) {
        throw new Error('Website too large for local viewing - download ZIP file instead');
      }

      if (!storedData.html) {
        throw new Error('Clone data not available - no HTML found');
      }

      // Reconstruct clone data from stored structure
      this.cloneData = {
        title: storedData.title,
        url: storedData.originalUrl,
        html: storedData.html,
        assets: storedData.assets || { images: [], stylesheets: [], scripts: [] },
        metadata: {
          timestamp: storedData.timestamp,
          depth: 1,
          assetsOnly: false
        }
      };
      
      // Debug logging
      this.debugCloneData();
      
      this.updateUrlInfo();
      
      // Process and display the cloned website
      await this.processAndDisplayWebsite();
      
    } catch (error) {
      console.error('Error loading clone data:', error);
      this.showError(error.message);
    }
  }
  
  debugCloneData() {
    console.log('Clone ID:', this.cloneId);
    console.log('Clone Data Structure:', this.cloneData);
    console.log('Assets:', {
      images: this.cloneData.assets.images?.length || 0,
      stylesheets: this.cloneData.assets.stylesheets?.length || 0,
      scripts: this.cloneData.assets.scripts?.length || 0
    });
    console.log('HTML length:', this.cloneData.html ? this.cloneData.html.length : 'No HTML');
  }
  
  updateUrlInfo() {
    if (this.cloneData) {
      this.urlInfo.textContent = `Viewing: ${this.cloneData.title || this.cloneData.url}`;
    }
  }
  
  async processAndDisplayWebsite() {
    try {
      console.log('Processing website for viewing...');
      
      // Create blob URLs for all assets
      await this.createBlobUrls();
      
      // Process HTML to use blob URLs
      const processedHtml = this.processHtmlForViewing();
      
      // Create blob URL for the processed HTML
      const htmlBlob = new Blob([processedHtml], { type: 'text/html' });
      const htmlBlobUrl = URL.createObjectURL(htmlBlob);
      
      // Store the HTML blob URL for cleanup
      this.blobUrls.set('main-html', htmlBlobUrl);
      
      // Load the processed HTML in the iframe
      this.viewerFrame.src = htmlBlobUrl;
      
      // Show the iframe and hide loading
      this.loading.classList.add('hidden');
      this.viewerFrame.classList.remove('hidden');
      
      console.log('Website loaded successfully');
      
    } catch (error) {
      console.error('Error processing website:', error);
      this.showError('Failed to process website: ' + error.message);
    }
  }
  
  async createBlobUrls() {
    console.log('Creating blob URLs for assets...');
    
    // Process images
    if (this.cloneData.assets.images) {
      for (const image of this.cloneData.assets.images) {
        if (image.dataUrl) {
          try {
            const response = await fetch(image.dataUrl);
            const blob = await response.blob();
            const blobUrl = URL.createObjectURL(blob);
            this.blobUrls.set(image.url, blobUrl);
            console.log('Created blob URL for image:', image.url);
          } catch (e) {
            console.warn('Failed to create blob URL for image:', image.url, e);
          }
        }
      }
    }
    
    // Process stylesheets - Handle inline stylesheets properly
    if (this.cloneData.assets.stylesheets) {
      for (let i = 0; i < this.cloneData.assets.stylesheets.length; i++) {
        const stylesheet = this.cloneData.assets.stylesheets[i];
        if (stylesheet.content && stylesheet.type === 'inline') {
          const blob = new Blob([stylesheet.content], { type: 'text/css' });
          const blobUrl = URL.createObjectURL(blob);
          // Use a unique identifier for inline styles
          this.blobUrls.set(`inline-style-${i}`, blobUrl);
          console.log('Created blob URL for inline stylesheet:', i);
        }
      }
    }
    
    // Process scripts - Handle inline scripts properly
    if (this.cloneData.assets.scripts) {
      for (let i = 0; i < this.cloneData.assets.scripts.length; i++) {
        const script = this.cloneData.assets.scripts[i];
        if (script.content && script.type === 'inline') {
          const blob = new Blob([script.content], { type: 'application/javascript' });
          const blobUrl = URL.createObjectURL(blob);
          // Use a unique identifier for inline scripts
          this.blobUrls.set(`inline-script-${i}`, blobUrl);
          console.log('Created blob URL for inline script:', i);
        }
      }
    }
    
    console.log('Total blob URLs created:', this.blobUrls.size);
  }
  
  processHtmlForViewing() {
    let html = this.cloneData.html;
    
    console.log('Processing HTML for viewing...');
    
    // Replace image URLs with blob URLs
    for (const [originalUrl, blobUrl] of this.blobUrls) {
      if (originalUrl !== 'main-html' && !originalUrl.startsWith('inline-')) {
        // Use a more precise replacement to avoid partial matches
        const escapedUrl = originalUrl.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        const regex = new RegExp(`(['"])${escapedUrl}\\1`, 'g');
        html = html.replace(regex, `$1${blobUrl}$1`);
        
        // Also handle URLs without quotes
        const regexNoQuotes = new RegExp(`\\b${escapedUrl}\\b`, 'g');
        html = html.replace(regexNoQuotes, blobUrl);
      }
    }
    
    // Inject inline styles as external stylesheets using blob URLs
    let styleIndex = 0;
    html = html.replace(/<style[^>]*>([\s\S]*?)<\/style>/gi, (match, content) => {
      const blobUrl = this.blobUrls.get(`inline-style-${styleIndex}`);
      styleIndex++;
      if (blobUrl) {
        console.log('Replacing inline style with blob URL:', blobUrl);
        return `<link rel="stylesheet" href="${blobUrl}">`;
      }
      return match;
    });
    
    // Inject inline scripts as external scripts using blob URLs
    let scriptIndex = 0;
    html = html.replace(/<script(?![^>]*src)[^>]*>([\s\S]*?)<\/script>/gi, (match, content) => {
      const blobUrl = this.blobUrls.get(`inline-script-${scriptIndex}`);
      scriptIndex++;
      if (blobUrl) {
        console.log('Replacing inline script with blob URL:', blobUrl);
        return `<script src="${blobUrl}"></script>`;
      }
      return match;
    });
    
    // Add base tag to handle relative URLs
    const baseTag = `<base href="${this.cloneData.url}">`;
    html = html.replace(/<head>/i, `<head>${baseTag}`);
    
    console.log('HTML processing complete');
    return html;
  }
  
  async downloadZip() {
    try {
      // Get the stored ZIP data
      const result = await chrome.storage.local.get([this.cloneId]);
      const storedData = result[this.cloneId];

      if (storedData && storedData.zipData) {
        // Create blob from ZIP data
        const zipBlob = new Blob([new Uint8Array(storedData.zipData)], { type: 'application/zip' });
        const downloadUrl = URL.createObjectURL(zipBlob);

        // Create a temporary link to trigger download
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = storedData.filename || 'website_clone.zip';
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Clean up the blob URL
        setTimeout(() => URL.revokeObjectURL(downloadUrl), 1000);
      } else {
        throw new Error('ZIP data not found');
      }
    } catch (error) {
      console.error('Download error:', error);
      alert('Failed to download ZIP file: ' + error.message);
    }
  }
  
  refreshViewer() {
    // Clear existing blob URLs
    for (const blobUrl of this.blobUrls.values()) {
      URL.revokeObjectURL(blobUrl);
    }
    this.blobUrls.clear();
    
    // Hide iframe and show loading
    this.viewerFrame.classList.add('hidden');
    this.error.classList.add('hidden');
    this.loading.classList.remove('hidden');
    
    // Reload the clone data
    this.loadCloneData();
  }
  
  showError(message) {
    this.loading.classList.add('hidden');
    this.viewerFrame.classList.add('hidden');
    this.errorMessage.textContent = message;
    this.error.classList.remove('hidden');
  }
  
  // Cleanup blob URLs when page unloads
  cleanup() {
    for (const blobUrl of this.blobUrls.values()) {
      URL.revokeObjectURL(blobUrl);
    }
  }
}

// Initialize viewer when page loads
document.addEventListener('DOMContentLoaded', () => {
  const viewer = new StokeViewer();
  
  // Cleanup on page unload
  window.addEventListener('beforeunload', () => {
    viewer.cleanup();
  });
});