// Popup functionality for Stoke Cloner Chrome Extension
class StokeExtension {
  constructor() {
    this.currentUrl = '';
    this.depth = 1;
    this.assetsOnly = false;
    this.isCloning = false;
    this.downloadUrl = null;
    this.cloneId = null;
    
    this.initializeElements();
    this.setupEventListeners();
    this.loadCurrentTab();
    this.loadSettings();
  }
  
  initializeElements() {
    this.urlInput = document.getElementById('url');
    this.editUrlBtn = document.getElementById('edit-url');
    this.depthSlider = document.getElementById('depth');
    this.assetsToggle = document.getElementById('assets-toggle');
    this.cloneBtn = document.getElementById('clone-btn');
    this.cloneText = document.getElementById('clone-text');
    this.loadingSpinner = document.getElementById('loading-spinner');
    this.progressSection = document.getElementById('progress-section');
    this.progressBar = document.getElementById('progress-bar');
    this.progressMessage = document.getElementById('progress-message');
    this.statusIcon = document.getElementById('status-icon');
    this.errorMessage = document.getElementById('error-message');
    this.actionButtons = document.getElementById('action-buttons');
    this.viewLocalBtn = document.getElementById('view-local-btn');
    this.downloadBtn = document.getElementById('download-btn');
    this.resetBtn = document.getElementById('reset-btn');
    this.settingsBtn = document.getElementById('settings-btn');
  }
  
  setupEventListeners() {
    this.cloneBtn.addEventListener('click', () => this.startCloning());
    this.editUrlBtn.addEventListener('click', () => this.toggleUrlEdit());
    this.depthSlider.addEventListener('input', (e) => {
      this.depth = parseInt(e.target.value);
      this.saveSettings();
    });
    this.assetsToggle.addEventListener('click', () => {
      this.assetsOnly = !this.assetsOnly;
      this.assetsToggle.classList.toggle('active', this.assetsOnly);
      this.saveSettings();
    });
    this.resetBtn.addEventListener('click', () => this.resetForm());
    this.viewLocalBtn.addEventListener('click', () => this.viewLocally());
    this.downloadBtn.addEventListener('click', () => this.downloadZip());
    this.settingsBtn.addEventListener('click', () => this.openSettings());
    
    // Handle URL input changes
    this.urlInput.addEventListener('change', () => {
      this.currentUrl = this.urlInput.value;
    });
  }
  
  async loadCurrentTab() {
    try {
      const response = await chrome.runtime.sendMessage({action: "getCurrentTab"});
      if (response && response.url) {
        this.currentUrl = response.url;
        this.urlInput.value = response.url;
      }
    } catch (error) {
      console.error('Failed to get current tab:', error);
      this.showError('Failed to get current tab URL');
    }
  }
  
  async loadSettings() {
    try {
      const result = await chrome.storage.sync.get(['depth', 'assetsOnly']);
      if (result.depth) {
        this.depth = result.depth;
        this.depthSlider.value = this.depth;
      }
      if (result.assetsOnly !== undefined) {
        this.assetsOnly = result.assetsOnly;
        this.assetsToggle.classList.toggle('active', this.assetsOnly);
      }
    } catch (error) {
      console.error('Failed to load settings:', error);
    }
  }
  
  async saveSettings() {
    try {
      await chrome.storage.sync.set({
        depth: this.depth,
        assetsOnly: this.assetsOnly
      });
    } catch (error) {
      console.error('Failed to save settings:', error);
    }
  }
  
  toggleUrlEdit() {
    const isReadonly = this.urlInput.hasAttribute('readonly');
    if (isReadonly) {
      this.urlInput.removeAttribute('readonly');
      this.urlInput.focus();
      this.urlInput.select();
      this.editUrlBtn.textContent = 'Save';
    } else {
      this.urlInput.setAttribute('readonly', true);
      this.currentUrl = this.urlInput.value;
      this.editUrlBtn.textContent = 'Edit';
    }
  }
  
  async startCloning() {
    if (this.isCloning || !this.currentUrl) return;
    
    this.isCloning = true;
    this.showProgress();
    this.updateProgress('Connecting to Stoke Cloner server...', 5);
    
    try {
      // Auto-add https:// if no protocol is specified
      let processedUrl = this.currentUrl.trim();
      if (!processedUrl.startsWith('http://') && !processedUrl.startsWith('https://')) {
        processedUrl = 'https://' + processedUrl;
      }
      
      const hostname = new URL(processedUrl).hostname;
      const cloneData = {
        url: processedUrl,
        depth: this.depth,
        assetsOnly: this.assetsOnly,
        hostname: hostname
      };
      
      this.updateProgress('Starting website crawl...', 10);
      
      const response = await chrome.runtime.sendMessage({
        action: "startCloning",
        data: cloneData
      });
      
      if (response && response.success) {
        this.updateProgress('Website cloned successfully!', 100, 'complete');

        // Create blob URL from the ZIP data
        const zipBlob = new Blob([new Uint8Array(response.zipData)], { type: 'application/zip' });
        this.downloadUrl = URL.createObjectURL(zipBlob);
        this.cloneId = response.cloneId;

        this.showActionButtons();
      } else {
        throw new Error(response?.error || 'Cloning failed');
      }
    } catch (error) {
      console.error('Cloning error:', error);
      this.updateProgress('Cloning failed', 0, 'error');
      this.showError(error.message);
      this.showResetButton();
    } finally {
      this.isCloning = false;
    }
  }
  
  showProgress() {
    this.progressSection.classList.remove('hidden');
    this.cloneBtn.disabled = true;
    this.cloneText.textContent = 'Cloning...';
    this.loadingSpinner.classList.remove('hidden');
  }
  
  updateProgress(message, progress, status = 'loading') {
    this.progressMessage.textContent = message;
    this.progressBar.style.width = `${progress}%`;
    
    // Update status icon
    this.statusIcon.className = `status-icon ${status}`;
    
    if (status === 'complete') {
      this.loadingSpinner.classList.add('hidden');
      this.cloneText.textContent = 'Complete!';
    }
  }
  
  showError(errorText) {
    this.errorMessage.textContent = errorText;
    this.errorMessage.classList.remove('hidden');
    this.loadingSpinner.classList.add('hidden');
    this.cloneText.textContent = 'Clone Website';
  }
  
  showActionButtons() {
    this.actionButtons.classList.remove('hidden');
  }
  
  showResetButton() {
    this.resetBtn.classList.remove('hidden');
  }
  
  resetForm() {
    this.progressSection.classList.add('hidden');
    this.actionButtons.classList.add('hidden');
    this.resetBtn.classList.add('hidden');
    this.errorMessage.classList.add('hidden');
    this.cloneBtn.disabled = false;
    this.cloneText.textContent = 'Clone Website';
    this.loadingSpinner.classList.add('hidden');
    this.isCloning = false;

    // Clean up blob URL if it exists
    if (this.downloadUrl) {
      URL.revokeObjectURL(this.downloadUrl);
      this.downloadUrl = null;
    }

    this.cloneId = null;
    this.progressBar.style.width = '0%';
  }
  
  async viewLocally() {
    try {
      this.viewLocalBtn.disabled = true;
      this.viewLocalBtn.innerHTML = '<div class="spinner"><svg class="animate-spin btn-icon" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg></div>Opening Viewer...';

      const response = await chrome.runtime.sendMessage({
        action: "viewLocally",
        cloneId: this.cloneId
      });

      if (response && response.success) {
        // Show success message
        this.showNotification(`✅ Opening cloned website in viewer`);
      } else {
        throw new Error(response?.error || 'Failed to open viewer');
      }
    } catch (error) {
      console.error('Viewer error:', error);
      this.showError('Failed to open viewer: ' + error.message);
    } finally {
      this.viewLocalBtn.disabled = false;
      this.viewLocalBtn.innerHTML = '<svg class="btn-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path></svg>View Locally';
    }
  }
  
  async downloadZip() {
    try {
      if (this.downloadUrl) {
        // Create a temporary link to trigger download
        const link = document.createElement('a');
        link.href = this.downloadUrl;
        link.download = `website_clone_${Date.now()}.zip`;
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        this.showNotification('✅ Download started!');
      } else {
        // Fallback: open downloads page
        await chrome.tabs.create({url: 'chrome://downloads/'});
        this.showNotification('✅ Check your downloads.');
      }
    } catch (error) {
      console.error('Download error:', error);
      this.showError('Failed to download ZIP file');
    }
  }
  
  showNotification(message) {
    // Simple notification - could be enhanced with a toast system
    const notification = document.createElement('div');
    notification.className = 'notification';
    notification.textContent = message;
    notification.style.cssText = `
      position: fixed;
      top: 10px;
      left: 10px;
      right: 10px;
      background: #10b981;
      color: white;
      padding: 8px 12px;
      border-radius: 6px;
      font-size: 12px;
      z-index: 1000;
    `;
    document.body.appendChild(notification);
    
    setTimeout(() => {
      notification.remove();
    }, 3000);
  }
  
  openSettings() {
    // Could open a settings page or show inline settings
    alert('Settings functionality can be expanded here');
  }
}

// Initialize when popup loads
document.addEventListener('DOMContentLoaded', () => {
  new StokeExtension();
});
