<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stoke Cloner - Website Viewer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #0f172a;
            color: white;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: #1e293b;
            border-bottom: 1px solid #334155;
            padding: 12px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-shrink: 0;
        }
        
        .header-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .logo {
            font-size: 18px;
            font-weight: 600;
            color: #f97316;
        }
        
        .url-info {
            font-size: 14px;
            color: #94a3b8;
        }
        
        .header-right {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn {
            background: #f97316;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .btn:hover {
            background: #ea580c;
        }
        
        .btn-secondary {
            background: #334155;
            color: #e2e8f0;
        }
        
        .btn-secondary:hover {
            background: #475569;
        }
        
        .viewer-container {
            flex: 1;
            position: relative;
            overflow: hidden;
        }
        
        .viewer-frame {
            width: 100%;
            height: 100%;
            border: none;
            background: white;
        }
        
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #334155;
            border-top: 4px solid #f97316;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error {
            background: #dc2626;
            color: white;
            padding: 16px;
            margin: 20px;
            border-radius: 8px;
            text-align: center;
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-left">
            <div class="logo">🔧 Stoke Cloner</div>
            <div class="url-info" id="url-info">Loading...</div>
        </div>
        <div class="header-right">
            <button class="btn btn-secondary" id="download-btn">Download ZIP</button>
            <button class="btn" id="refresh-btn">Refresh</button>
        </div>
    </div>
    
    <div class="viewer-container">
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <div>Loading cloned website...</div>
        </div>
        
        <div class="error hidden" id="error">
            <h3>Error Loading Website</h3>
            <p id="error-message">Failed to load the cloned website.</p>
        </div>
        
        <iframe class="viewer-frame hidden" id="viewer-frame" sandbox="allow-scripts allow-same-origin allow-forms"></iframe>
    </div>
    
    <script src="viewer.js"></script>
</body>
</html>
