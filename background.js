// Background service worker for Stoke Cloner Chrome Extension
// Import JSZip for creating ZIP files
importScripts('jszip.min.js');

// Test JSZip availability
console.log('JSZip loaded:', typeof JSZip !== 'undefined');
if (typeof JSZip !== 'undefined') {
  console.log('JSZip version:', JSZip.version || 'unknown');
}

// Extension installation and setup
chrome.runtime.onInstalled.addListener(() => {
  console.log('Stoke Cloner extension installed');
  
  // Create context menu
  chrome.contextMenus.create({
    id: "cloneWebsite",
    title: "Clone this website with Stoke Cloner",
    contexts: ["page"]
  });
  
  // Set default settings
  chrome.storage.sync.set({
    depth: 1,
    assetsOnly: false
  });
});

// Handle context menu clicks
chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === "cloneWebsite") {
    // Open the extension popup
    chrome.action.openPopup();
  }
});

// Handle messages from popup and content scripts
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('Background received message:', request);
  
  switch (request.action) {
    case "getCurrentTab":
      handleGetCurrentTab(sendResponse);
      return true; // Keep message channel open for async response
      
    case "startCloning":
      handleCloning(request.data, sendResponse);
      return true;
      
    case "viewLocally":
      handleViewLocally(request.cloneId, sendResponse);
      return true;
      
    default:
      console.warn('Unknown action:', request.action);
      sendResponse({success: false, error: 'Unknown action'});
  }
});

// Get current active tab information
async function handleGetCurrentTab(sendResponse) {
  try {
    const tabs = await chrome.tabs.query({active: true, currentWindow: true});
    if (tabs.length > 0) {
      const tab = tabs[0];
      sendResponse({
        success: true,
        url: tab.url,
        title: tab.title,
        id: tab.id
      });
    } else {
      sendResponse({success: false, error: 'No active tab found'});
    }
  } catch (error) {
    console.error('Error getting current tab:', error);
    sendResponse({success: false, error: error.message});
  }
}

// Handle website cloning using standalone client-side approach
async function handleCloning(data, sendResponse) {
  try {
    console.log('Starting standalone cloning process for:', data.url);

    // Start the cloning process
    const cloneResult = await cloneWebsiteStandalone(data);

    if (cloneResult.success) {
      // Generate filename
      const filename = `${data.hostname.replace(/[^a-zA-Z0-9]/g, '_')}_clone_${Date.now()}.zip`;

      // Store minimal data for potential local viewing (avoid quota issues)
      const cloneId = `clone_${Date.now()}`;

      if (cloneResult.tooLarge) {
        // For large files, store only basic info
        await chrome.storage.local.set({
          [cloneId]: {
            filename: filename,
            originalUrl: data.url,
            timestamp: Date.now(),
            title: cloneResult.cloneData.title,
            tooLarge: true
          }
        });
      } else {
        // Store full data for smaller files
        await chrome.storage.local.set({
          [cloneId]: {
            zipData: cloneResult.zipData,
            filename: filename,
            originalUrl: data.url,
            timestamp: Date.now(),
            title: cloneResult.cloneData.title,
            // Store only essential clone data to avoid quota issues
            html: cloneResult.cloneData.html,
            assets: {
              images: cloneResult.cloneData.assets.images.filter(img => img.dataUrl), // Only images with data
              stylesheets: cloneResult.cloneData.assets.stylesheets.filter(css => css.type === 'inline'), // Only inline CSS
              scripts: cloneResult.cloneData.assets.scripts.filter(js => js.type === 'inline') // Only inline JS
            }
          }
        });
      }

      sendResponse({
        success: true,
        zipData: cloneResult.zipData,
        filename: filename,
        cloneId: cloneId
      });
    } else {
      throw new Error(cloneResult.error);
    }

  } catch (error) {
    console.error('Cloning error:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

// Handle local viewing using extension viewer page
async function handleViewLocally(cloneId, sendResponse) {
  try {
    console.log('Opening local viewer for clone:', cloneId);

    // Get clone data from storage
    const result = await chrome.storage.local.get([cloneId]);
    const cloneData = result[cloneId];

    if (!cloneData) {
      throw new Error('Clone data not found');
    }

    // Create viewer URL
    const viewerUrl = chrome.runtime.getURL(`viewer.html?cloneId=${cloneId}`);

    // Open viewer in new tab
    const tab = await chrome.tabs.create({
      url: viewerUrl
    });

    sendResponse({
      success: true,
      viewerUrl: viewerUrl,
      tabId: tab.id
    });

  } catch (error) {
    console.error('Local viewer error:', error);
    sendResponse({
      success: false,
      error: 'Failed to open local viewer: ' + error.message
    });
  }
}

// Clean up old download URLs to prevent memory leaks
chrome.runtime.onStartup.addListener(async () => {
  try {
    const items = await chrome.storage.local.get();
    const now = Date.now();
    const oneDay = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
    
    for (const [key, value] of Object.entries(items)) {
      if (key.startsWith('download_') && value.timestamp) {
        if (now - value.timestamp > oneDay) {
          // Clean up old download data
          await chrome.storage.local.remove(key);
          if (value.url) {
            URL.revokeObjectURL(value.url);
          }
        }
      }
    }
  } catch (error) {
    console.error('Error cleaning up old downloads:', error);
  }
});

// Handle download completion
chrome.downloads.onChanged.addListener((downloadDelta) => {
  if (downloadDelta.state && downloadDelta.state.current === 'complete') {
    console.log('Download completed:', downloadDelta.id);
  }
});

// Standalone website cloning functionality
async function cloneWebsiteStandalone(data) {
  try {
    console.log('Starting standalone clone for:', data.url);

    // Get the current tab to inject content script
    const tabs = await chrome.tabs.query({active: true, currentWindow: true});
    if (tabs.length === 0) {
      throw new Error('No active tab found');
    }

    const tabId = tabs[0].id;

    // Inject and execute the cloning script
    const results = await chrome.scripting.executeScript({
      target: {tabId: tabId},
      function: performStandaloneClone,
      args: [data]
    });

    if (!results || results.length === 0) {
      throw new Error('Failed to execute cloning script');
    }

    const cloneResult = results[0].result;

    if (!cloneResult.success) {
      throw new Error(cloneResult.error);
    }

    // Create ZIP file from collected data
    const zipBlob = await createZipFromCloneData(cloneResult.data);

    // Store the blob data as array buffer for transfer
    const arrayBuffer = await zipBlob.arrayBuffer();
    const uint8Array = new Uint8Array(arrayBuffer);

    // Check size limit (Chrome storage quota is ~10MB)
    const zipDataArray = Array.from(uint8Array);
    const estimatedSize = JSON.stringify(zipDataArray).length;

    if (estimatedSize > 5 * 1024 * 1024) { // 5MB limit to be safe
      console.warn('ZIP file too large for storage, will only provide download');
      return {
        success: true,
        zipData: zipDataArray,
        cloneData: cloneResult.data,
        tooLarge: true
      };
    }

    return {
      success: true,
      zipData: zipDataArray,
      cloneData: cloneResult.data
    };

  } catch (error) {
    console.error('Standalone cloning error:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Function to be injected into the page for cloning
function performStandaloneClone(data) {
  return new Promise(async (resolve) => {
    try {
      console.log('Performing standalone clone on page:', window.location.href);

      const cloneData = {
        url: window.location.href,
        title: document.title,
        html: document.documentElement.outerHTML,
        assets: {
          stylesheets: [],
          scripts: [],
          images: [],
          fonts: [],
          other: []
        },
        metadata: {
          timestamp: Date.now(),
          depth: data.depth,
          assetsOnly: data.assetsOnly
        }
      };

      // Collect stylesheets (only inline ones to avoid CORS)
      const stylesheets = document.querySelectorAll('style');
      for (const stylesheet of stylesheets) {
        cloneData.assets.stylesheets.push({
          url: 'inline',
          content: stylesheet.textContent,
          type: 'inline'
        });
      }

      // Collect external stylesheet URLs (without content to avoid CORS)
      const externalStylesheets = document.querySelectorAll('link[rel="stylesheet"]');
      for (const stylesheet of externalStylesheets) {
        if (stylesheet.href) {
          cloneData.assets.stylesheets.push({
            url: stylesheet.href,
            content: null, // Don't fetch content due to CORS
            type: 'external'
          });
        }
      }

      // Collect images (only same-origin or data URLs to avoid CORS)
      const images = document.querySelectorAll('img[src]');
      for (const img of images) {
        if (img.src) {
          if (img.src.startsWith('data:')) {
            // Include data URLs directly
            cloneData.assets.images.push({
              url: img.src,
              dataUrl: img.src,
              alt: img.alt || '',
              width: img.naturalWidth || img.width,
              height: img.naturalHeight || img.height,
              type: 'data'
            });
          } else if (img.src.startsWith(window.location.origin)) {
            // Try to fetch same-origin images
            try {
              const response = await fetch(img.src);
              const blob = await response.blob();
              const dataUrl = await blobToDataUrl(blob);
              cloneData.assets.images.push({
                url: img.src,
                dataUrl: dataUrl,
                alt: img.alt || '',
                width: img.naturalWidth || img.width,
                height: img.naturalHeight || img.height,
                type: 'same-origin'
              });
            } catch (e) {
              console.warn('Failed to fetch same-origin image:', img.src, e);
              // Include URL reference only
              cloneData.assets.images.push({
                url: img.src,
                dataUrl: null,
                alt: img.alt || '',
                width: img.naturalWidth || img.width,
                height: img.naturalHeight || img.height,
                type: 'external'
              });
            }
          } else {
            // External images - just store URL reference
            cloneData.assets.images.push({
              url: img.src,
              dataUrl: null,
              alt: img.alt || '',
              width: img.naturalWidth || img.width,
              height: img.naturalHeight || img.height,
              type: 'external'
            });
          }
        }
      }

      // Collect scripts (only inline ones to avoid CORS)
      if (!data.assetsOnly) {
        const inlineScripts = document.querySelectorAll('script:not([src])');
        for (const script of inlineScripts) {
          if (script.textContent.trim()) {
            cloneData.assets.scripts.push({
              url: 'inline',
              content: script.textContent,
              type: 'inline'
            });
          }
        }

        // Collect external script URLs (without content)
        const externalScripts = document.querySelectorAll('script[src]');
        for (const script of externalScripts) {
          if (script.src) {
            cloneData.assets.scripts.push({
              url: script.src,
              content: null, // Don't fetch content due to CORS
              type: 'external'
            });
          }
        }
      }

      resolve({
        success: true,
        data: cloneData
      });

    } catch (error) {
      console.error('Clone execution error:', error);
      resolve({
        success: false,
        error: error.message
      });
    }
  });

  // Helper function to convert blob to data URL
  function blobToDataUrl(blob) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  }
}

// Create ZIP file from collected clone data
async function createZipFromCloneData(cloneData) {
  try {
    console.log('Creating ZIP file with JSZip version:', JSZip.version || 'unknown');

    // Test JSZip basic functionality
    const zip = new JSZip();
    console.log('JSZip instance created successfully');

    // Add main HTML file
    let processedHtml = cloneData.html;
    zip.file('index.html', processedHtml);
    console.log('HTML file added to ZIP');

    // Process and add stylesheets
    let combinedCSS = '';
    for (const stylesheet of cloneData.assets.stylesheets) {
      if (stylesheet.type === 'inline' && stylesheet.content) {
        combinedCSS += '\n/* Inline Style */\n' + stylesheet.content + '\n';
      }
      // External stylesheets are kept as references (no content due to CORS)
    }

    if (combinedCSS) {
      zip.file('assets/css/inline_styles.css', combinedCSS);
    }

    // Process and add images (only those with data)
    for (let i = 0; i < cloneData.assets.images.length; i++) {
      const image = cloneData.assets.images[i];
      if (image.dataUrl && (image.type === 'data' || image.type === 'same-origin')) {
        const extension = getExtensionFromDataUrl(image.dataUrl);
        const filename = `assets/images/image_${i}.${extension}`;

        try {
          // Convert data URL to blob
          const response = await fetch(image.dataUrl);
          const blob = await response.blob();
          zip.file(filename, blob);

          // Replace URL in HTML
          processedHtml = processedHtml.replace(
            new RegExp(escapeRegExp(image.url), 'g'),
            filename
          );
        } catch (e) {
          console.warn('Failed to process image:', image.url, e);
        }
      }
      // External images without data are kept as original URLs
    }

    // Process and add scripts (only inline ones)
    let combinedJS = '';
    for (const script of cloneData.assets.scripts) {
      if (script.type === 'inline' && script.content) {
        combinedJS += '\n/* Inline Script */\n' + script.content + '\n';
      }
      // External scripts are kept as references (no content due to CORS)
    }

    if (combinedJS) {
      zip.file('assets/js/inline_scripts.js', combinedJS);
    }

    // Update the main HTML file with processed content
    zip.file('index.html', processedHtml);

    // Add metadata file
    zip.file('clone_info.json', JSON.stringify({
      originalUrl: cloneData.url,
      title: cloneData.title,
      timestamp: cloneData.metadata.timestamp,
      depth: cloneData.metadata.depth,
      assetsOnly: cloneData.metadata.assetsOnly
    }, null, 2));

    // Generate ZIP blob
    console.log('Generating ZIP file...');
    const zipBlob = await zip.generateAsync({
      type: 'blob',
      compression: 'DEFLATE',
      compressionOptions: {
        level: 6
      }
    });
    console.log('ZIP file generated successfully, size:', zipBlob.size);
    return zipBlob;

  } catch (error) {
    console.error('Error creating ZIP:', error);
    console.error('Error stack:', error.stack);
    throw error;
  }
}

// Helper functions
function getFilenameFromUrl(url) {
  try {
    const urlObj = new URL(url);
    let filename = urlObj.pathname.split('/').pop();
    if (!filename || !filename.includes('.')) {
      filename = 'file_' + Date.now() + '.css';
    }
    return filename.replace(/[^a-zA-Z0-9.-]/g, '_');
  } catch (e) {
    return 'file_' + Date.now() + '.css';
  }
}

function getExtensionFromDataUrl(dataUrl) {
  const match = dataUrl.match(/data:image\/([^;]+)/);
  return match ? match[1] : 'png';
}

function escapeRegExp(string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

// Error handling for unhandled promise rejections
self.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection in background script:', event.reason);
});
