<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <link rel="stylesheet" href="popup.css">
</head>
<body class="bg-slate-900 text-white">
  <div class="container">
    <!-- Header -->
    <div class="header">
      <h1 class="title">Stoke Cloner</h1>
    </div>

    <!-- URL Display (Auto-filled from current tab) -->
    <div class="input-group">
      <label class="label">Website URL</label>
      <div class="url-container">
        <input type="url" id="url" class="url-input" readonly>
        <button id="edit-url" class="edit-btn">Edit</button>
      </div>
    </div>

    <!-- Crawling Depth -->
    <div class="input-group">
      <label class="label">Crawling Depth</label>
      <div class="slider-container">
        <input type="range" id="depth" min="1" max="3" value="1" class="slider">
        <div class="slider-labels">
          <span>1</span><span>2</span><span>3</span>
        </div>
      </div>
      <p class="help-text">Higher depth = more complete clone but slower</p>
    </div>

    <!-- Assets Only Toggle -->
    <div class="input-group">
      <div class="toggle-container">
        <div class="toggle-info">
          <h3 class="toggle-title">Assets Only</h3>
          <p class="toggle-description">Media files only</p>
        </div>
        <button id="assets-toggle" class="toggle-switch">
          <span class="toggle-slider"></span>
        </button>
      </div>
    </div>

    <!-- Clone Button -->
    <button id="clone-btn" class="clone-button">
      <span id="clone-text">Clone Website</span>
      <div id="loading-spinner" class="spinner hidden">
        <svg class="animate-spin" width="20" height="20" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      </div>
    </button>

    <!-- Progress Section -->
    <div id="progress-section" class="progress-container hidden">
      <div class="progress-content">
        <div class="progress-header">
          <div id="status-icon" class="status-icon"></div>
          <span id="progress-message" class="progress-text">Starting...</span>
        </div>
        <div id="progress-bar-container" class="progress-bar-bg">
          <div id="progress-bar" class="progress-bar-fill"></div>
        </div>
        <div id="error-message" class="error-container hidden"></div>
        
        <!-- Action Buttons -->
        <div id="action-buttons" class="action-buttons hidden">
          <button id="view-local-btn" class="action-btn primary">
            <svg class="btn-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
            </svg>
            View Locally
          </button>
          <button id="download-btn" class="action-btn secondary">
            <svg class="btn-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            Download ZIP
          </button>
        </div>
        
        <button id="reset-btn" class="reset-button hidden">Clone Another Website</button>
      </div>
    </div>

    <!-- Footer -->
    <div class="footer">
      <button id="settings-btn" class="settings-link">Settings</button>
    </div>
  </div>

  <script src="popup.js"></script>
</body>
</html>
